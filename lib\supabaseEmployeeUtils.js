/**
 * Supabase Employee Management Utilities
 * Handles all employee-related database operations
 */

import { supabase, supabaseAdmin, TABLES } from './supabase.js';

/**
 * Get all employees
 * @param {boolean} activeOnly - If true, only return active employees
 * @returns {Promise<Array>} Array of employee objects
 */
export async function getEmployees(activeOnly = false) {
  try {
    let query = supabase
      .from(TABLES.EMPLOYEES)
      .select('*')
      .order('name');

    if (activeOnly) {
      query = query.eq('is_active', true);
    }

    const { data, error } = await query;

    if (error) {
      console.error('Error fetching employees:', error);
      throw new Error(`Failed to fetch employees: ${error.message}`);
    }

    return data || [];
  } catch (error) {
    console.error('Error in getEmployees:', error);
    throw error;
  }
}

/**
 * Get a single employee by ID
 * @param {string} employeeId - The employee ID
 * @returns {Promise<Object|null>} Employee object or null if not found
 */
export async function getEmployeeById(employeeId) {
  try {
    const { data, error } = await supabase
      .from(TABLES.EMPLOYEES)
      .select('*')
      .eq('id', employeeId)
      .single();

    if (error) {
      if (error.code === 'PGRST116') {
        return null; // Employee not found
      }
      console.error('Error fetching employee:', error);
      throw new Error(`Failed to fetch employee: ${error.message}`);
    }

    return data;
  } catch (error) {
    console.error('Error in getEmployeeById:', error);
    throw error;
  }
}

/**
 * Create a new employee
 * @param {Object} employeeData - Employee data
 * @returns {Promise<Object>} Created employee object
 */
export async function createEmployee(employeeData) {
  try {
    // Validate required fields
    if (!employeeData.name || employeeData.name.trim() === '') {
      throw new Error('Employee name is required');
    }

    // Prepare data for insertion
    const insertData = {
      name: employeeData.name.trim(),
      email: employeeData.email && employeeData.email.trim() !== '' ? employeeData.email.trim() : null,
      role: employeeData.role || null,
      department: employeeData.department || null,
      phone: employeeData.phone || null,
      is_active: employeeData.is_active !== undefined ? employeeData.is_active : true,
      specializations: employeeData.specializations || [],
      working_hours: employeeData.working_hours || {
        monday: { start: "09:00", end: "18:00" },
        tuesday: { start: "09:00", end: "18:00" },
        wednesday: { start: "09:00", end: "18:00" },
        thursday: { start: "09:00", end: "18:00" },
        friday: { start: "09:00", end: "18:00" },
        saturday: null,
        sunday: null
      },
      notes: employeeData.notes || null
    };

    const { data, error } = await supabaseAdmin
      .from(TABLES.EMPLOYEES)
      .insert([insertData])
      .select()
      .single();

    if (error) {
      console.error('Error creating employee:', error);
      throw new Error(`Failed to create employee: ${error.message}`);
    }

    return data;
  } catch (error) {
    console.error('Error in createEmployee:', error);
    throw error;
  }
}

/**
 * Update an existing employee
 * @param {string} employeeId - The employee ID
 * @param {Object} employeeData - Updated employee data
 * @returns {Promise<Object>} Updated employee object
 */
export async function updateEmployee(employeeId, employeeData) {
  try {
    // Validate required fields
    if (!employeeData.name || employeeData.name.trim() === '') {
      throw new Error('Employee name is required');
    }

    // Prepare data for update
    const updateData = {
      name: employeeData.name.trim(),
      email: employeeData.email && employeeData.email.trim() !== '' ? employeeData.email.trim() : null,
      role: employeeData.role || null,
      department: employeeData.department || null,
      phone: employeeData.phone || null,
      is_active: employeeData.is_active !== undefined ? employeeData.is_active : true,
      specializations: employeeData.specializations || [],
      working_hours: employeeData.working_hours || null,
      notes: employeeData.notes || null,
      updated_at: new Date().toISOString()
    };

    const { data, error } = await supabaseAdmin
      .from(TABLES.EMPLOYEES)
      .update(updateData)
      .eq('id', employeeId)
      .select()
      .single();

    if (error) {
      console.error('Error updating employee:', error);
      throw new Error(`Failed to update employee: ${error.message}`);
    }

    return data;
  } catch (error) {
    console.error('Error in updateEmployee:', error);
    throw error;
  }
}

/**
 * Delete an employee (soft delete by setting is_active to false)
 * @param {string} employeeId - The employee ID
 * @returns {Promise<boolean>} True if successful
 */
export async function deleteEmployee(employeeId) {
  try {
    // Check if employee has any appointments
    const { data: appointments, error: appointmentsError } = await supabase
      .from(TABLES.APPOINTMENTS)
      .select('id')
      .eq('employee_id', employeeId)
      .limit(1);

    if (appointmentsError) {
      console.error('Error checking employee appointments:', appointmentsError);
      throw new Error(`Failed to check employee appointments: ${appointmentsError.message}`);
    }

    // If employee has appointments, soft delete (deactivate)
    if (appointments && appointments.length > 0) {
      const { error } = await supabaseAdmin
        .from(TABLES.EMPLOYEES)
        .update({ is_active: false, updated_at: new Date().toISOString() })
        .eq('id', employeeId);

      if (error) {
        console.error('Error deactivating employee:', error);
        throw new Error(`Failed to deactivate employee: ${error.message}`);
      }
    } else {
      // If no appointments, hard delete
      const { error } = await supabaseAdmin
        .from(TABLES.EMPLOYEES)
        .delete()
        .eq('id', employeeId);

      if (error) {
        console.error('Error deleting employee:', error);
        throw new Error(`Failed to delete employee: ${error.message}`);
      }
    }

    return true;
  } catch (error) {
    console.error('Error in deleteEmployee:', error);
    throw error;
  }
}

/**
 * Get employees by specialization
 * @param {string} specialization - The specialization to filter by
 * @returns {Promise<Array>} Array of employee objects
 */
export async function getEmployeesBySpecialization(specialization) {
  try {
    const { data, error } = await supabase
      .from(TABLES.EMPLOYEES)
      .select('*')
      .eq('is_active', true)
      .contains('specializations', [specialization])
      .order('name');

    if (error) {
      console.error('Error fetching employees by specialization:', error);
      throw new Error(`Failed to fetch employees: ${error.message}`);
    }

    return data || [];
  } catch (error) {
    console.error('Error in getEmployeesBySpecialization:', error);
    throw error;
  }
}

/**
 * Get employee statistics
 * @returns {Promise<Object>} Statistics object
 */
export async function getEmployeeStats() {
  try {
    const { data: allEmployees, error: allError } = await supabase
      .from(TABLES.EMPLOYEES)
      .select('id, is_active, department');

    if (allError) {
      console.error('Error fetching employee stats:', allError);
      throw new Error(`Failed to fetch employee stats: ${allError.message}`);
    }

    const total = allEmployees.length;
    const active = allEmployees.filter(emp => emp.is_active).length;
    const inactive = total - active;

    // Count by department
    const departmentCounts = allEmployees.reduce((acc, emp) => {
      const dept = emp.department || 'Non Specificato';
      acc[dept] = (acc[dept] || 0) + 1;
      return acc;
    }, {});

    return {
      total,
      active,
      inactive,
      departmentCounts
    };
  } catch (error) {
    console.error('Error in getEmployeeStats:', error);
    throw error;
  }
}
